"""
Universal Process Manager.
Contains universal functions used by storage, order, and product modules:
- pickup_process: Universal pickup process with payment support
- payment_process: Universal payment process with storno support  
- select_sections: Universal section selection process
"""

import logging
import asyncio
import httpx
from datetime import datetime
from typing import List, Tuple, Dict, Any

from managers.ws_manager import ws_manager
from managers.session_manager import session_manager
from config import device_config

logger = logging.getLogger(__name__)
from managers.sequence_manager import SequenceManager
from managers.session_manager import SectionConfig


async def payment_process_v2(amount: float) -> bool:
    """
    Universal payment process function.
    Simple function that processes payment and returns success/failure.

    Args:
        amount: Payment amount in currency units

    Returns:
        bool: True if payment successful, False if failed
    """
    logger.info(f"Starting universal payment process for amount: {amount}")

    if amount <= 0:
        logger.info("No payment needed (amount <= 0)")
        return True

    try:
        # Prepare payment data (same format as existing code)
        payment_data = {
            "type": "sale",
            "amount": float(amount),
            "variable_symbol": f"payment_{int(datetime.now().timestamp())}"  # Unique identifier
        }

        logger.info(f"Processing payment: {payment_data}")

        # Get payment service configuration
        payment_service_timeout = device_config.payment_config.get("payment_service_timeout", 30)
        payment_service_url = device_config.payment_config.get("payment_service_url")

        if not payment_service_url:
            logger.error("Payment service URL not configured")
            return False

        # Send payment request to payment service
        async with httpx.AsyncClient(timeout=payment_service_timeout) as client:
            response = await client.post(
                payment_service_url,
                json=payment_data
            )

            if response.status_code == 200:
                logger.info(f"Payment successful: {response.status_code} - {response.text}")
                return True
            else:
                logger.error(f"Payment failed: {response.status_code} - {response.text}")
                return False

    except httpx.TimeoutException:
        logger.error(f"Payment service timeout for amount {amount}")
        return False

    except Exception as e:
        logger.error(f"Payment error: {e}")
        return False
sequence_manager = SequenceManager()




async def payment_process(session_id: str, amount: float, message_queue: asyncio.Queue) -> bool:
    logger.info("Payment required, starting payment process")

    # Send start payment screen message
    await ws_manager.send(session_id, {
        "type": "start_payment_screen",
        "wait_for_ready": True
    })

    # Wait for payment_screen_ready
    while True:
        try:
            message = await message_queue.get()
        except Exception as e:
            logger.error(f"Error waiting for payment message: {e}")
            return False

        message_type = message.get("type")
        logger.info(f"Processing payment message: {message_type}")

        if message_type == "payment_screen_ready":
            logger.info("Payment screen ready - starting payment terminal call")

            # Send payment initiation status
            await ws_manager.send(session_id, {
                "type": "payment_status",
                "status": "initiating"
            })

            # Get payment amount from session
            session = session_manager.get_session(session_id)
            amount = getattr(session, 'amount', 0) if session else 0

            # Call payment terminal (this initiates payment but doesn't wait for result)
            payment_initiated = await payment_process_v2(amount)

            if payment_initiated:
                # Send payment processing status
                await ws_manager.send(session_id, {
                    "type": "payment_status",
                    "status": "processing"
                })
                logger.info("Payment initiated successfully, waiting for callback")
                # Continue waiting for payment_status callback - don't break here
            else:
                # Payment terminal call failed
                await ws_manager.send(session_id, {
                    "type": "payment_result",
                    "success": False,
                    "message": "Payment failed to initiate"
                })
                return False

        elif message_type == "payment_status":
            # Handle payment status from payment callback
            logger.info("Received payment status callback")

            success = message.get("success", False)

            # Send payment result
            await ws_manager.send(session_id, {
                "type": "payment_result",
                "success": success,
                "message": "Payment successful" if success else "Payment failed"
            })

            if success:
                logger.info("Payment completed successfully - proceeding to pickup loop")
                break
            else:
                logger.info("Payment failed - ending pickup process")
                return False

        elif message_type == "storno":
            logger.info("Payment cancelled by user")
            return False






async def pickup_process(sections: List[int], session_id: str, message_queue: asyncio.Queue, requires_payment: bool = False) -> Tuple[bool, List[int]]:
    """
    Universal pickup process function used by storage, order, and product modules.

    This function handles ALL pickup-related WebSocket messages:
    - payment_screen_ready: processes payment if required
    - hardware_screen_ready: starts FSM sequence for all sections, then ends pickup process
    - open_section: starts FSM sequence for individual section
    - hardware_screen_stop: ends pickup process

    The pickup process also ends when WebSocket connection is closed.

    Args:
        sections: List of section IDs to pickup
        session_id: WebSocket session ID
        message_queue: Queue to receive WebSocket messages
        requires_payment: Whether payment is required before pickup

    Returns:
        Tuple of (success, successful_sections)
    """
    logger.info(f"Starting pickup_process for {len(sections)} sections: {sections}")
    
    picking_up = True
    successful_sections = []

    # Register message queue for universal payment callback injection
    register_message_queue(session_id, message_queue)

    # Import sequence manager for FSM operations

    try:
        # Handle payment if required using simple payment_process
        if requires_payment:
            # Get amount from session
            session = session_manager.get_session(session_id)
            amount = getattr(session, 'amount', 0) if session else 0

            payment_success = await payment_process(session_id=session_id, amount=amount, message_queue=message_queue)
            if not payment_success:
                logger.info("Payment failed or cancelled - ending pickup process")
                return False, []
            
        # Only send start_hardware_screen if we have sections to open
        if sections:
            await ws_manager.send(session_id, {
                "type": "start_hardware_screen",
                "wait_for_ready": True
            })
        else:
            # No sections to open, payment-only mode - return success immediately
            logger.info("Payment-only mode completed successfully (no sections to open)")
            return True, []
        
        # Main pickup loop - handle all message types
        while picking_up:
            # Check if WebSocket connection is still active
            if not ws_manager.is_connected(session_id):
                logger.info(f"WebSocket disconnected - ending pickup process for session {session_id}")
                # Clean up session when WebSocket disconnects
                await session_manager.remove_session(session_id)
                return True, successful_sections

            try:
                # Wait for message from websocket - no timeout
                message = await message_queue.get()
            except Exception as e:
                logger.info(f"WebSocket disconnected or error waiting for message in pickup_process: {e}")
                # End pickup process when WebSocket disconnects and clean up session
                logger.info(f"Ending pickup process due to WebSocket disconnection for session {session_id}")
                await session_manager.remove_session(session_id)
                return True, successful_sections

            message_type = message.get("type")
            logger.info(f"Processing pickup_process message: {message_type}")

            if message_type == "hardware_screen_ready":
                # Start FSM pickup sequence and open all sections automatically each after each
                logger.info(f"Hardware screen ready - starting FSM sequence for {len(sections)} sections")

                # Send acknowledgment that hardware screen ready was received
                await ws_manager.send(session_id, {
                    "type": "hardware_status",
                    "status":"preparing",
                    "message": f"Připravuji schránku"
                })

                # Convert section IDs to SectionConfig objects
                section_configs = []
                for section_id in sections:
                    section_configs.append(SectionConfig(
                        section_id=section_id,
                        lock_id=section_id,  # Default to same as section_id
                        is_tempered=True,  # Default to tempered for pickup operations
                        led_section=section_id  # Set LED section to same as section_id
                    ))

                # Start FSM sequence - this will open all sections automatically
                sequence_started = await sequence_manager.start_fsm_sequence(
                    session_id=session_id,
                    sections=section_configs,
                    pin="pickup_process"
                )

                if sequence_started:
                    logger.info(f"FSM sequence started successfully, waiting for completion")

                    # Wait for the FSM sequence to complete by waiting for the task
                    if session_id in sequence_manager.active_sequences:
                        try:
                            # Wait for the sequence task to complete
                            await sequence_manager.active_sequences[session_id]
                            logger.info(f"FSM sequence completed successfully for {len(sections)} sections")
                            successful_sections.extend(sections)
                        except Exception as e:
                            logger.error(f"FSM sequence failed with error: {e}")
                    else:
                        logger.error("FSM sequence task not found in active sequences")
                else:
                    logger.error("Failed to start FSM sequence for pickup process")

                return True, successful_sections

            elif message_type == "open_section":
                section_id = message.get("section_id")
                if section_id not in sections:
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "Invalid section"
                    })
                    continue

                # Use start_fsm_sequence for individual section opening
                logger.info(f"Opening individual section: {section_id}")

               # Send acknowledgment that hardware screen ready was received
                await ws_manager.send(session_id, {
                    "type": "hardware_status",
                    "status":"preparing",
                    "message": f"Připravuji schránku"
                })

                section_config = SectionConfig(
                    section_id=section_id,
                    lock_id=section_id,  # Default to same as section_id
                    is_tempered=True,  # Default to tempered for pickup operations
                    led_section=section_id
                )

                success = await sequence_manager.start_fsm_sequence(
                    session_id=session_id,
                    sections=[section_config],
                    pin="pickup_process_single"
                )

                if success:
                    if section_id not in successful_sections:
                        successful_sections.append(section_id)

                    # Send success message to client
                    await ws_manager.send(session_id, {
                        "type": "section_result",
                        "success": True,
                        "section_id": section_id,
                        "message": f"Section {section_id} opened successfully"
                    })
                else:
                    # Send failure message to client
                    await ws_manager.send(session_id, {
                        "type": "section_result",
                        "success": False,
                        "section_id": section_id,
                        "message": f"Failed to open section {section_id}"
                    })

            elif message_type == "hardware_screen_stop":
                # Stop pickup sequence or end pickup process
                logger.info(f"Received hardware_screen_stop command - ending pickup process for session {session_id}")
                picking_up = False

                # Delete websocket session and clean up
                logger.info(f"Cleaning up WebSocket session for {session_id}")
                ws_manager.disconnect(session_id)
                await session_manager.remove_session(session_id)

                return True, successful_sections

        return True, successful_sections
        
    except Exception as e:
        logger.error(f"Error in pickup_process: {e}")
        return False, []
    finally:
        # Clean up universal message queue registration
        unregister_message_queue(session_id)


# Global registry for active message queues (for universal payment callback injection)
_active_message_queues: Dict[str, asyncio.Queue] = {}


def register_message_queue(session_id: str, message_queue: asyncio.Queue) -> None:
    """
    Register a message queue for payment callback injection.
    Used by all modules (storage, product, order) that need payment callbacks.

    Args:
        session_id: Session ID to register queue for
        message_queue: The asyncio.Queue to register
    """
    _active_message_queues[session_id] = message_queue
    logger.info(f"Registered message queue for payment callbacks: {session_id}")


def unregister_message_queue(session_id: str) -> None:
    """
    Unregister a message queue for payment callback injection.
    Should be called when a session ends.

    Args:
        session_id: Session ID to unregister queue for
    """
    if session_id in _active_message_queues:
        _active_message_queues.pop(session_id)
        logger.info(f"Unregistered message queue for payment callbacks: {session_id}")


async def inject_payment_callback(session_id: str, status: str, message: str = "") -> bool:
    """
    Universal payment callback injection function.
    Injects payment status directly into the registered message queue for any module.

    Args:
        session_id: Session ID to inject callback for
        status: Payment status ("success" or "failed")
        message: Optional message from payment service

    Returns:
        bool: True if injection successful, False otherwise
    """
    logger.info(f"Universal payment callback injection for session {session_id}: {status}")

    try:
        # Check if we have an active message queue for this session
        if session_id not in _active_message_queues:
            logger.error(f"No active message queue found for payment callback: {session_id}")
            return False

        message_queue = _active_message_queues[session_id]

        # Create payment status message
        success = status == "success"
        callback_message = {
            "type": "payment_status",
            "success": success,
            "message": message or ("Payment successful" if success else "Payment failed")
        }

        # Inject the callback message into the queue
        await message_queue.put(callback_message)
        logger.info(f"Payment callback injected successfully for session {session_id}")

        return True

    except Exception as e:
        logger.error(f"Error injecting payment callback for session {session_id}: {e}")
        return False


async def handle_payment_callback(session_id: str, status: str, message: str = "") -> bool:
    """
    Universal payment callback handler for all modules.
    Simply calls inject_payment_callback to inject the callback into the message queue.

    Args:
        session_id: Session ID to handle callback for
        status: Payment status ("success" or "failed")
        message: Optional message from payment service

    Returns:
        bool: True if callback handled successfully, False otherwise
    """
    logger.info(f"Universal payment callback handler for session {session_id}: {status}")
    return await inject_payment_callback(session_id, status, message)


# Old complex payment_process function removed - now using simple payment_process(amount) function


async def select_sections(enabled_sections: List[int], available_sections: List[int], session_id: str, message_queue: asyncio.Queue, wait_for_stop: bool = True) -> Tuple[bool, List[int]]:
    """
    Universal section selection function used by order and other modules.
    Works exactly like pickup_process() but for section selection.

    Args:
        enabled_sections: List of enabled section IDs (usually all available)
        available_sections: List of available section IDs to select from
        session_id: WebSocket session ID
        message_queue: Queue to receive WebSocket messages
        wait_for_stop: Whether to wait for stop_selection command (default: True)

    Returns:
        Tuple of (success, selected_sections)
    """
    logger.info(f"Starting section selection for session {session_id} with {len(available_sections)} available sections: {available_sections}")

    selecting = True
    successful_sections = []

    # Register message queue for universal payment callback injection
    register_message_queue(session_id, message_queue)

    # Import sequence manager for FSM operations

    try:
        # No payment required, go directly to hardware screen
        await ws_manager.send(session_id, {
            "type": "start_hardware_screen",
            "wait_for_ready": True
        })
    
        # Main selection loop - handle all message types
        while selecting:
            # Check if WebSocket connection is still active
            if not ws_manager.is_connected(session_id):
                logger.info(f"WebSocket disconnected - ending section selection for session {session_id}")
                # Clean up session when WebSocket disconnects
                await session_manager.remove_session(session_id)
                return True, successful_sections

            try:
                # Wait for message from websocket - no timeout
                message = await message_queue.get()
            except Exception as e:
                logger.info(f"WebSocket disconnected or error waiting for message in select_sections: {e}")
                # End selection process when WebSocket disconnects and clean up session
                logger.info(f"Ending section selection due to WebSocket disconnection for session {session_id}")
                await session_manager.remove_session(session_id)
                return True, successful_sections

            message_type = message.get("type")
            logger.info(f"Processing pickup_process message: {message_type}")

            if message_type == "hardware_screen_ready":
                # Start FSM pickup sequence and open all sections automatically each after each
                logger.info(f"Hardware screen ready - starting FSM sequence for {len(enabled_sections)} sections")

                # Send acknowledgment that hardware screen ready was received
                await ws_manager.send(session_id, {
                    "type": "hardware_status",
                    "status":"preparing",
                    "message": f"Připravuji schránku"
                })

                # Convert section IDs to SectionConfig objects
                section_configs = []
                for section_id in enabled_sections:
                    section_configs.append(SectionConfig(
                        section_id=section_id,
                        lock_id=section_id,  # Default to same as section_id
                        is_tempered=True,  # Default to tempered for pickup operations
                        led_section=section_id  # Set LED section to same as section_id
                    ))

                # Start FSM sequence - this will open all sections automatically
                sequence_started = await sequence_manager.start_fsm_sequence(
                    session_id=session_id,
                    sections=section_configs,
                    pin="pickup_process"
                )

                if sequence_started:
                    logger.info(f"FSM sequence started successfully, waiting for completion")

                    # Wait for the FSM sequence to complete by waiting for the task
                    if session_id in sequence_manager.active_sequences:
                        try:
                            # Wait for the sequence task to complete
                            await sequence_manager.active_sequences[session_id]
                            logger.info(f"FSM sequence completed successfully for {len(enabled_sections)} sections")
                            successful_sections.extend(enabled_sections)
                        except Exception as e:
                            logger.error(f"FSM sequence failed with error: {e}")
                    else:
                        logger.error("FSM sequence task not found in active sequences")
                else:
                    logger.error("Failed to start FSM sequence for pickup process")

            elif message_type == "open_section":
                section_id = message.get("section_id")
                if section_id not in available_sections:
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "Invalid section"
                    })
                    continue

                # Use start_fsm_sequence for individual section opening
                logger.info(f"Opening individual section: {section_id}")

               # Send acknowledgment that hardware screen ready was received
                await ws_manager.send(session_id, {
                    "type": "hardware_status",
                    "status":"preparing",
                    "message": f"Připravuji schránku"
                })

                section_config = SectionConfig(
                    section_id=section_id,
                    lock_id=section_id,  # Default to same as section_id
                    is_tempered=True,  # Default to tempered for pickup operations
                    led_section=section_id
                )

                success = await sequence_manager.start_fsm_sequence(
                    session_id=session_id,
                    sections=[section_config],
                    pin="pickup_process_single"
                )

                if success:
                    if section_id not in successful_sections:
                        successful_sections.append(section_id)

                    # Send success message to client
                    await ws_manager.send(session_id, {
                        "type": "section_result",
                        "success": True,
                        "section_id": section_id,
                        "message": f"Section {section_id} opened successfully"
                    })
                else:
                    # Send failure message to client
                    await ws_manager.send(session_id, {
                        "type": "section_result",
                        "success": False,
                        "section_id": section_id,
                        "message": f"Failed to open section {section_id}"
                    })

            elif message_type == "hardware_screen_stop":
                # Stop selection sequence or end selection process
                logger.info(f"Received hardware_screen_stop command - ending selection process for session {session_id}")
                selecting = False

                # Delete websocket session and clean up
                logger.info(f"Cleaning up WebSocket session for {session_id}")
                ws_manager.disconnect(session_id)
                await session_manager.remove_session(session_id)

                return True, successful_sections

        return True, successful_sections

    except Exception as e:
        logger.error(f"Error in select_sections: {e}")
        return False, []
    finally:
        # Clean up universal message queue registration
        unregister_message_queue(session_id)


# Old _process_payment_terminal function removed - now using simple payment_process(amount) function
